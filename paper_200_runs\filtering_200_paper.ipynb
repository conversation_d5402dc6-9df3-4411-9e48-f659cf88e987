import pandas as pd
import os

paper_list = pd.read_excel("paper_200_list.xlsx", sheet_name = "paper_200")

paper_list.head()

len(paper_list['old'])

import pandas as pd
import os
import shutil
from pathlib import Path

# Extract paper IDs from the paper_list DataFrame
# Assuming the paper IDs are in a column - adjust column name as needed
paper_ids = paper_list['old'].tolist()  # or whatever column contains the IDs

print(f"Starting XML file organization process...")
print(f"Total paper IDs to process: {len(paper_ids)}")
print("=" * 50)

# Define source directories
source_directories = ["articles", "reviews"]
target_dir = "paper_200_runs"

# Ensure target directory exists
os.makedirs(target_dir, exist_ok=True)

# Initialize counters
xml_files_copied = 0
paper_ids_found = 0
paper_ids_not_found = []

# Process each paper ID
for paper_id in paper_ids:
    found_xml = False
    
    # Search for XML files in source directories
    for source_dir in source_directories:
        if os.path.exists(source_dir):
            xml_file_path = os.path.join(source_dir, f"{paper_id}.xml")
            
            if os.path.exists(xml_file_path):
                target_file_path = os.path.join(target_dir, f"{paper_id}.xml")
                
                try:
                    shutil.copy2(xml_file_path, target_file_path)
                    print(f"✓ Copied: {paper_id}.xml from {source_dir}")
                    xml_files_copied += 1
                    found_xml = True
                    break  # Found the XML, no need to check other directories
                except Exception as e:
                    print(f"✗ Failed to copy {paper_id}.xml: {str(e)}")
    
    if found_xml:
        paper_ids_found += 1
    else:
        print(f"✗ No XML file found for {paper_id}")
        paper_ids_not_found.append(paper_id)

# Summary report
print("\n" + "=" * 50)
print("XML FILE ORGANIZATION SUMMARY")
print("=" * 50)
print(f"Total paper IDs processed: {len(paper_ids)}")
print(f"Paper IDs with XML found: {paper_ids_found}")
print(f"XML files successfully copied: {xml_files_copied}")
print(f"Paper IDs not found: {len(paper_ids_not_found)}")

if paper_ids_not_found:
    print(f"\nMissing XML files for {len(paper_ids_not_found)} paper IDs:")
    for missing_id in paper_ids_not_found:
        print(f"  - {missing_id}")

print(f"\nTarget directory: {target_dir}")
print("Process completed!")

# Optional: Save missing IDs to a file for reference
if paper_ids_not_found:
    missing_df = pd.DataFrame(paper_ids_not_found, columns=['missing_paper_id'])
    missing_df.to_csv(f"{target_dir}/missing_xml_files.csv", index=False)
    print(f"Missing paper IDs saved to: {target_dir}/missing_xml_files.csv")