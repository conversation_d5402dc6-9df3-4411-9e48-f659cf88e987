import matplotlib.pyplot as plt
import pandas as pd

file_path = "precision_summary_20250902_070458.xlsx"

df = pd.read_excel(file_path)

df.head()

df['n_EP'] = df['TPs'] + df['FNs'] + df['FPs']
df.head()

plt.figure(figsize=(8, 6))
plt.scatter(df['precision'], range(len(df)), s=df['n_EP']*5, alpha=0.7)
plt.xlabel('Precision')
plt.ylabel('Paper Index')
plt.title('Precision vs Paper Index (Dot Size = n_EP)')
plt.show()

import matplotlib.pyplot as plt

plt.figure(figsize=(8, 6))
scatter = plt.scatter(df['n_EP'], df['precision'], color='blue')
plt.axhline(y=0.7, color='red', linestyle='--', linewidth=2)

# Add custom legend
plt.legend([
    'Data Points',
    'Precision = 0.7'
])

plt.xlabel('No of Endpoints')
plt.ylabel('Precision')
plt.title('Scatterplot of Precision vs. No of Endpoints')
plt.grid(True)
plt.show()

import matplotlib.pyplot as plt

plt.figure(figsize=(8, 6))
scatter = plt.scatter(df['n_EP'], df['precision'], color='blue', label='Data Points')

# Draw axhline and store returned Line2D object
precision_line = plt.axhline(y=0.7, color='red', linestyle='--', linewidth=2)
# Manually add legend
plt.legend(['Data Points', 'Precision = 0.7'])

plt.xlabel('n_EP')
plt.ylabel('Precision')
plt.title('Scatterplot of Precision vs. n_EP')
plt.grid(True)
plt.show()

ep_df = pd.read_excel("EP_precision.xlsx")
ep_df.head()


plt.figure(figsize=(8, 6))

# Make a scatter plot: Precision vs. Row Labels, point size by No. of Endpoints
# Since Row Labels are strings, plot them as categories on the y-axis
plt.scatter(ep_df['Precision'], ep_df['Row Labels'], 
            s=ep_df['No. of Endpoints']*10,                # scale size for visibility
            alpha=0.7, color='blue')

plt.xlabel('Precision')
plt.ylabel('Assay Type')
plt.title('Bubble Plot: Assay Type vs Precision; Dot Size = No. of Endpoints')
plt.grid(True, axis='x')

# Optionally, annotate with the endpoint count
for i in range(len(ep_df)):
    plt.annotate(str(ep_df.loc[i, 'No. of Endpoints']), 
                 (ep_df.loc[i, 'Precision'], ep_df.loc[i, 'Row Labels']),
                 textcoords="offset points", xytext=(5,-5), ha='left', fontsize=8)

plt.show()

import math
import re
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats

# ---- 1) Create the dataframe from a list of dicts (paste your rows here) ----
rows = [
    {"id":"w4387939270","Total":2,"reg_precision":0,"reg_TP":0,"reg_FP":2,"reg_FN":36,"hum_precision":50,"hum_TP":1,"hum_FP":1,"hum_FN":36,"Validation Status":"Completed","Curated By":"Ramya","Type":"Review","Phase":"Scale_Up"},
    {"id":"w2913433668","Total":2,"reg_precision":0,"reg_TP":0,"reg_FP":2,"reg_FN":62,"hum_precision":0,"hum_TP":0,"hum_FP":2,"hum_FN":62,"Validation Status":"Completed","Curated By":"Ramya","Type":"Review","Phase":"Scale_Up"},
    {"id":"w4389882757","Total":10,"reg_precision":0.1,"reg_TP":1,"reg_FP":9,"reg_FN":33,"hum_precision":90,"hum_TP":9,"hum_FP":1,"hum_FN":22,"Validation Status":"Completed","Curated By":"Mugdha","Type":"Research","Phase":"AI_POC"},
    {"id":"w2784139266","Total":5,"reg_precision":0.2,"reg_TP":1,"reg_FP":4,"reg_FN":10,"hum_precision":80,"hum_TP":4,"hum_FP":1,"hum_FN":10,"Validation Status":"Completed","Curated By":"Ramya","Type":"Research","Phase":"AI_POC"},
    {"id":"w1983386052","Total":5,"reg_precision":0.2,"reg_TP":1,"reg_FP":4,"reg_FN":32,"hum_precision":20,"hum_TP":1,"hum_FP":4,"hum_FN":32,"Validation Status":"Completed","Curated By":"Ramya","Type":"Review","Phase":"Scale_Up"},
    {"id":"w2911746982","Total":14,"reg_precision":0.357,"reg_TP":5,"reg_FP":9,"reg_FN":6,"hum_precision":42.86,"hum_TP":6,"hum_FP":8,"hum_FN":2,"Validation Status":"Completed","Curated By":"Ramya","Type":"Research","Phase":"AI_POC"},
    {"id":"w2886837533","Total":5,"reg_precision":0.4,"reg_TP":2,"reg_FP":3,"reg_FN":9,"hum_precision":100,"hum_TP":5,"hum_FP":0,"hum_FN":9,"Validation Status":"Completed","Curated By":"Mugdha","Type":"Review","Phase":"Scale_Up"},
    {"id":"w4225380120","Total":7,"reg_precision":0.429,"reg_TP":3,"reg_FP":4,"reg_FN":11,"hum_precision":100,"hum_TP":7,"hum_FP":0,"hum_FN":5,"Validation Status":"Completed","Curated By":"Mugdha","Type":"Research","Phase":"Scale_Up"},
    {"id":"w2034382557","Total":8,"reg_precision":0.5,"reg_TP":4,"reg_FP":4,"reg_FN":7,"hum_precision":62.5,"hum_TP":5,"hum_FP":3,"hum_FN":5,"Validation Status":"Completed","Curated By":"Mugdha","Type":"Research","Phase":"Scale_Up"},
    {"id":"w3033351565","Total":2,"reg_precision":0.5,"reg_TP":1,"reg_FP":1,"reg_FN":6,"hum_precision":50,"hum_TP":1,"hum_FP":1,"hum_FN":8,"Validation Status":"Completed","Curated By":"Mugdha","Type":"Review","Phase":"Scale_Up"},
    {"id":"w4283659311","Total":2,"reg_precision":0.5,"reg_TP":1,"reg_FP":1,"reg_FN":5,"hum_precision":50,"hum_TP":1,"hum_FP":1,"hum_FN":3,"Validation Status":"Completed","Curated By":"Mugdha","Type":"Research","Phase":"Scale_Up"},
    {"id":"w4376958886","Total":2,"reg_precision":0.5,"reg_TP":1,"reg_FP":1,"reg_FN":68,"hum_precision":50,"hum_TP":1,"hum_FP":1,"hum_FN":np.nan,"Validation Status":"Completed","Curated By":"Mugdha","Type":"Research","Phase":"AI_POC"},
    {"id":"w4390743855","Total":21,"reg_precision":0.524,"reg_TP":11,"reg_FP":10,"reg_FN":33,"hum_precision":90.48,"hum_TP":19,"hum_FP":2,"hum_FN":19,"Validation Status":"Completed","Curated By":"Mugdha","Type":"Research","Phase":"AI_POC"},
    {"id":"w4398201134","Total":11,"reg_precision":0.545,"reg_TP":6,"reg_FP":5,"reg_FN":18,"hum_precision":90.91,"hum_TP":10,"hum_FP":1,"hum_FN":14,"Validation Status":"Completed","Curated By":"Mugdha","Type":"Research","Phase":"Scale_Up"},
    {"id":"w4386861187","Total":14,"reg_precision":0.571,"reg_TP":8,"reg_FP":6,"reg_FN":29,"hum_precision":78.57,"hum_TP":11,"hum_FP":3,"hum_FN":25,"Validation Status":"Completed","Curated By":"Mugdha","Type":"Research","Phase":"Scale_Up"},
    {"id":"w3157341523","Total":7,"reg_precision":0.571,"reg_TP":4,"reg_FP":3,"reg_FN":2,"hum_precision":42.86,"hum_TP":3,"hum_FP":4,"hum_FN":1,"Validation Status":"Completed","Curated By":"Mugdha","Type":"Research","Phase":"Scale_Up"},
    {"id":"w4226097966","Total":58,"reg_precision":0.586,"reg_TP":34,"reg_FP":24,"reg_FN":57,"hum_precision":74.14,"hum_TP":43,"hum_FP":15,"hum_FN":28,"Validation Status":"Completed","Curated By":"Mugdha","Type":"Research","Phase":"AI_POC"},
    {"id":"w2088635881","Total":20,"reg_precision":0.6,"reg_TP":12,"reg_FP":8,"reg_FN":21,"hum_precision":95,"hum_TP":19,"hum_FP":1,"hum_FN":16,"Validation Status":"Completed","Curated By":"Pravie","Type":"Research","Phase":"Scale_Up"},
    {"id":"w2059265067","Total":10,"reg_precision":0.6,"reg_TP":6,"reg_FP":4,"reg_FN":10,"hum_precision":90,"hum_TP":9,"hum_FP":1,"hum_FN":9,"Validation Status":"Completed","Curated By":"Mugdha","Type":"Research","Phase":"Scale_Up"},
    {"id":"w4392643092","Total":10,"reg_precision":0.6,"reg_TP":6,"reg_FP":4,"reg_FN":38,"hum_precision":80,"hum_TP":8,"hum_FP":2,"hum_FN":35,"Validation Status":"Completed","Curated By":"Mugdha","Type":"Research","Phase":"Scale_Up"},
    {"id":"w3195418414","Total":12,"reg_precision":0.75,"reg_TP":9,"reg_FP":3,"reg_FN":2,"hum_precision":66.67,"hum_TP":8,"hum_FP":4,"hum_FN":3,"Validation Status":"Completed","Curated By":"Mugdha","Type":"Research","Phase":"Scale_Up"},
    {"id":"w3043062793","Total":9,"reg_precision":0.778,"reg_TP":7,"reg_FP":2,"reg_FN":7,"hum_precision":77.78,"hum_TP":7,"hum_FP":2,"hum_FN":6,"Validation Status":"Completed","Curated By":"Mugdha","Type":"Research","Phase":"Scale_Up"},
    {"id":"w2746254865","Total":10,"reg_precision":0.8,"reg_TP":8,"reg_FP":2,"reg_FN":15,"hum_precision":100,"hum_TP":10,"hum_FP":0,"hum_FN":13,"Validation Status":"Completed","Curated By":"Ramya","Type":"Research","Phase":"AI_POC"},
    {"id":"w3090364682","Total":5,"reg_precision":0.8,"reg_TP":4,"reg_FP":1,"reg_FN":3,"hum_precision":100,"hum_TP":5,"hum_FP":0,"hum_FN":0,"Validation Status":"Completed","Curated By":"Ramya","Type":"Research","Phase":"AI_POC"},
    {"id":"w3083782960","Total":15,"reg_precision":0.8,"reg_TP":12,"reg_FP":3,"reg_FN":10,"hum_precision":66.67,"hum_TP":10,"hum_FP":5,"hum_FN":3,"Validation Status":"Completed","Curated By":"Pravie","Type":"Research","Phase":"Scale_Up"},
    {"id":"w2778226119","Total":19,"reg_precision":0.842,"reg_TP":16,"reg_FP":3,"reg_FN":10,"hum_precision":94.74,"hum_TP":18,"hum_FP":1,"hum_FN":5,"Validation Status":"Completed","Curated By":"Ramya","Type":"Research","Phase":"AI_POC"},
    {"id":"w2314772071","Total":26,"reg_precision":0.846,"reg_TP":22,"reg_FP":4,"reg_FN":43,"hum_precision":100,"hum_TP":26,"hum_FP":0,"hum_FN":24,"Validation Status":"Completed","Curated By":"Mugdha","Type":"Research","Phase":"AI_POC"},
    {"id":"w3093537091","Total":33,"reg_precision":0.848,"reg_TP":28,"reg_FP":5,"reg_FN":18,"hum_precision":90.91,"hum_TP":30,"hum_FP":3,"hum_FN":6,"Validation Status":"Completed","Curated By":"Ramya","Type":"Research","Phase":"AI_POC"},
    {"id":"w4226170358","Total":36,"reg_precision":0.861,"reg_TP":31,"reg_FP":5,"reg_FN":31,"hum_precision":88.89,"hum_TP":32,"hum_FP":4,"hum_FN":15,"Validation Status":"Completed","Curated By":"Mugdha","Type":"Research","Phase":"AI_POC"},
    {"id":"w4387230410","Total":10,"reg_precision":0.9,"reg_TP":9,"reg_FP":1,"reg_FN":30,"hum_precision":70,"hum_TP":7,"hum_FP":3,"hum_FN":22,"Validation Status":"Completed","Curated By":"Ramya","Type":"Research","Phase":"AI_POC"},
    {"id":"w4385978315","Total":11,"reg_precision":0.909,"reg_TP":10,"reg_FP":1,"reg_FN":16,"hum_precision":90.91,"hum_TP":10,"hum_FP":1,"hum_FN":12,"Validation Status":"Completed","Curated By":"Mugdha","Type":"Research","Phase":"AI_POC"},
    {"id":"w2907078722","Total":18,"reg_precision":0.944,"reg_TP":17,"reg_FP":1,"reg_FN":15,"hum_precision":100,"hum_TP":18,"hum_FP":0,"hum_FN":11,"Validation Status":"Completed","Curated By":"Pravie","Type":"Research","Phase":"Scale_Up"},
    {"id":"w2969941297","Total":19,"reg_precision":0.947,"reg_TP":18,"reg_FP":1,"reg_FN":11,"hum_precision":94.74,"hum_TP":18,"hum_FP":1,"hum_FN":4,"Validation Status":"Completed","Curated By":"Ramya","Type":"Research","Phase":"AI_POC"},
    {"id":"w4284897854","Total":21,"reg_precision":0.952,"reg_TP":20,"reg_FP":1,"reg_FN":35,"hum_precision":76.19,"hum_TP":16,"hum_FP":5,"hum_FN":23,"Validation Status":"Completed","Curated By":"Ramya","Type":"Research","Phase":"AI_POC"},
    {"id":"w4210421364","Total":23,"reg_precision":0.957,"reg_TP":22,"reg_FP":1,"reg_FN":34,"hum_precision":95.65,"hum_TP":22,"hum_FP":1,"hum_FN":17,"Validation Status":"Completed","Curated By":"Ramya","Type":"Research","Phase":"AI_POC"},
    {"id":"w4381943608","Total":1,"reg_precision":1.0,"reg_TP":1,"reg_FP":0,"reg_FN":1,"hum_precision":100,"hum_TP":1,"hum_FP":0,"hum_FN":1,"Validation Status":"Completed","Curated By":"Mugdha","Type":"Review","Phase":"Scale_Up"},
    {"id":"w4392158917","Total":17,"reg_precision":1.0,"reg_TP":17,"reg_FP":0,"reg_FN":6,"hum_precision":100,"hum_TP":17,"hum_FP":0,"hum_FN":7,"Validation Status":"Completed","Curated By":"Mugdha","Type":"Research","Phase":"Scale_Up"},
    {"id":"w2119870604","Total":9,"reg_precision":1.0,"reg_TP":9,"reg_FP":0,"reg_FN":8,"hum_precision":100,"hum_TP":9,"hum_FP":0,"hum_FN":8,"Validation Status":"Completed","Curated By":"Ramya","Type":"Research","Phase":"AI_POC"},
    {"id":"w3001098362","Total":3,"reg_precision":1.0,"reg_TP":3,"reg_FP":0,"reg_FN":3,"hum_precision":100,"hum_TP":3,"hum_FP":0,"hum_FN":3,"Validation Status":"Completed","Curated By":"Ramya","Type":"Research","Phase":"AI_POC"},
    {"id":"w4280631979","Total":8,"reg_precision":1.0,"reg_TP":8,"reg_FP":0,"reg_FN":25,"hum_precision":87.5,"hum_TP":7,"hum_FP":1,"hum_FN":21,"Validation Status":"Completed","Curated By":"Ramya","Type":"Research","Phase":"AI_POC"},
]
df = pd.DataFrame(rows)

# ---- 2) Normalize precision to 0-1 scale and recompute from TP/FP ----
def to_unit(p):
    # Accepts numbers like 0.8, 80, "90.48"; converts to 0-1
    if pd.isna(p):
        return np.nan
    try:
        v = float(p)
    except:
        v = float(re.sub(r'[^0-9.\-eE]', '', str(p)))
    return v/100.0 if v > 1 else v

df["reg_precision_raw"] = df["reg_precision"].apply(to_unit)
df["hum_precision_raw"] = df["hum_precision"].apply(to_unit)

# Recompute precision from counts: TP/(TP+FP), guarding divide-by-zero
df["reg_precision_calc"] = df.apply(lambda r: (r["reg_TP"]/(r["reg_TP"]+r["reg_FP"])) if (r["reg_TP"]+r["reg_FP"])>0 else np.nan, axis=1)
df["hum_precision_calc"] = df.apply(lambda r: (r["hum_TP"]/(r["hum_TP"]+r["hum_FP"])) if (r["hum_TP"]+r["hum_FP"])>0 else np.nan, axis=1)

# ---- 3) Flag inconsistencies between reported and computed precision ----
def diff_ok(a, b, tol=0.01):
    if pd.isna(a) or pd.isna(b):
        return True
    return abs(a-b) <= tol

df["reg_precision_mismatch"] = ~df.apply(lambda r: diff_ok(r["reg_precision_raw"], r["reg_precision_calc"]), axis=1)
df["hum_precision_mismatch"] = ~df.apply(lambda r: diff_ok(r["hum_precision_raw"], r["hum_precision_calc"]), axis=1)

# Use computed precision going forward to ensure consistency
df["reg_p"] = df["reg_precision_calc"]
df["hum_p"] = df["hum_precision_calc"]

# ---- 4) Per-id comparison metrics ----
df["delta"] = df["reg_p"] - df["hum_p"]                  # positive => regression higher
df["abs_error"] = df["delta"].abs()
df["ape"] = df.apply(lambda r: abs(r["delta"])/r["hum_p"] if r["hum_p"] not in [0, np.nan] else np.nan, axis=1)

# ---- 5) Overall summary ----
summary = {
    "n": int(df[["reg_p","hum_p"]].dropna().shape[0]),
    "mean_reg_precision": float(df["reg_p"].mean(skipna=True)),
    "mean_hum_precision": float(df["hum_p"].mean(skipna=True)),
    "median_reg_precision": float(df["reg_p"].median(skipna=True)),
    "median_hum_precision": float(df["hum_p"].median(skipna=True)),
    "MAE": float(df["abs_error"].mean(skipna=True)),
    "MAPE": float(df["ape"].mean(skipna=True)),
    "Pearson_r": float(df[["reg_p","hum_p"]].corr(method="pearson").iloc[0,1]),
    "Spearman_rho": float(df[["reg_p","hum_p"]].corr(method="spearman").iloc[0,1]),
    "reg_better_count": int((df["delta"]>0).sum()),
    "human_better_count": int((df["delta"]<0).sum()),
    "ties": int((df["delta"]==0).sum()),
    "any_reg_mismatch": bool(df["reg_precision_mismatch"].any()),
    "any_hum_mismatch": bool(df["hum_precision_mismatch"].any()),
}
print("Overall summary:")
for k,v in summary.items():
    print(f" - {k}: {v}")

# Paired t-test and Wilcoxon to check mean/median difference
paired = df.dropna(subset=["reg_p","hum_p"])
tstat, tp = stats.ttest_rel(paired["reg_p"], paired["hum_p"])
wstat, wp = stats.wilcoxon(paired["reg_p"], paired["hum_p"])
print(f"\nPaired t-test: t={tstat:.3f}, p={tp:.4g}")
print(f"Wilcoxon signed-rank: W={wstat:.3f}, p={wp:.4g}")

# ---- 6) Stratified summaries by Type and Phase ----
def group_summary(gdf):
    return pd.Series({
        "n": gdf.shape[0],
        "mean_reg_p": gdf["reg_p"].mean(),
        "mean_hum_p": gdf["hum_p"].mean(),
        "MAE": gdf["abs_error"].mean(),
        "Pearson_r": gdf[["reg_p","hum_p"]].corr(method="pearson").iloc[0,1] if gdf.shape[0] >= 2 else np.nan
    })

by_type = df.groupby("Type", dropna=False).apply(group_summary)
by_phase = df.groupby("Phase", dropna=False).apply(group_summary)
print("\nBy Type:\n", by_type)
print("\nBy Phase:\n", by_phase)

# ---- 7) Visualizations ----
sns.set(style="whitegrid")

# Scatter with identity line
plt.figure(figsize=(6,6))
sns.scatterplot(data=df, x="hum_p", y="reg_p", hue="Phase", style="Type", s=80)
lims = [0,1]
plt.plot(lims, lims, 'k--', alpha=0.6)
plt.xlim(lims); plt.ylim(lims)
plt.xlabel("Human precision")
plt.ylabel("Regression precision")
plt.title("Regression vs Human Precision")
plt.legend(bbox_to_anchor=(1.05,1), loc="upper left")
plt.tight_layout()

# Bland–Altman style plot (difference vs mean)
plt.figure(figsize=(7,5))
m = (df["reg_p"] + df["hum_p"])/2
d = df["reg_p"] - df["hum_p"]
sns.scatterplot(x=m, y=d, hue=df["Phase"], s=80)
mean_diff = np.nanmean(d)
sd_diff = np.nanstd(d, ddof=1)
for y in [mean_diff, mean_diff+1.96*sd_diff, mean_diff-1.96*sd_diff]:
    plt.axhline(y, color="red", linestyle="--", alpha=0.6)
plt.axhline(0, color="k", linestyle=":")
plt.xlabel("Mean precision (reg, human)")
plt.ylabel("Difference (reg - human)")
plt.title("Agreement: Bland–Altman")
plt.tight_layout()

# Absolute error by Phase
plt.figure(figsize=(7,4))
sns.boxplot(data=df, x="Phase", y="abs_error")
sns.swarmplot(data=df, x="Phase", y="abs_error", color="k", size=3, alpha=0.5)
plt.title("Absolute error |reg - human| by Phase")
plt.tight_layout()

plt.show()

# ---- 8) Export results if needed ----
# df.to_csv("precision_comparison_by_id.csv", index=False)
# by_type.to_csv("precision_summary_by_type.csv")
# by_phase.to_csv("precision_summary_by_phase.csv")